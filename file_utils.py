"""
File upload and processing utilities.
"""
import os
import uuid
import aiofiles
from typing import Op<PERSON>, <PERSON><PERSON>
from PIL import Image
from fastapi import HTTP<PERSON>x<PERSON>, UploadFile
from config import settings

# Ensure upload directory exists
os.makedirs(settings.UPLOAD_DIR, exist_ok=True)

def validate_image_file(file: UploadFile) -> bool:
    """Validate if the uploaded file is a valid image."""
    if not file.filename:
        return False
    
    # Check file extension
    file_extension = file.filename.lower().split('.')[-1]
    if file_extension not in settings.ALLOWED_IMAGE_EXTENSIONS:
        return False
    
    # Check file size
    if file.size and file.size > settings.MAX_FILE_SIZE:
        return False
    
    return True

def generate_unique_filename(original_filename: str) -> str:
    """Generate a unique filename while preserving the extension."""
    if not original_filename:
        return f"{uuid.uuid4()}.jpg"
    
    file_extension = original_filename.lower().split('.')[-1]
    if file_extension not in settings.ALLOWED_IMAGE_EXTENSIONS:
        file_extension = "jpg"
    
    return f"{uuid.uuid4()}.{file_extension}"

async def save_uploaded_image(file: UploadFile) -> Tuple[str, str]:
    """
    Save uploaded image file and return the file path and URL.
    Returns: (file_path, file_url)
    """
    if not validate_image_file(file):
        raise HTTPException(
            status_code=400,
            detail="Invalid image file. Supported formats: JPEG, PNG, GIF, WebP. Max size: 5MB"
        )
    
    # Generate unique filename
    filename = generate_unique_filename(file.filename)
    file_path = os.path.join(settings.UPLOAD_DIR, filename)
    
    # Save file
    async with aiofiles.open(file_path, 'wb') as f:
        content = await file.read()
        await f.write(content)
    
    # Generate URL (assuming files are served from /uploads/ endpoint)
    file_url = f"/uploads/{filename}"
    
    return file_path, file_url

def resize_image_if_needed(file_path: str, max_width: int = 1024, max_height: int = 1024) -> None:
    """Resize image if it's larger than specified dimensions."""
    try:
        with Image.open(file_path) as img:
            # Check if resize is needed
            if img.width <= max_width and img.height <= max_height:
                return
            
            # Calculate new dimensions maintaining aspect ratio
            ratio = min(max_width / img.width, max_height / img.height)
            new_width = int(img.width * ratio)
            new_height = int(img.height * ratio)
            
            # Resize and save
            resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
            resized_img.save(file_path, optimize=True, quality=85)
            
    except Exception as e:
        # If resize fails, keep original file
        print(f"Failed to resize image {file_path}: {e}")

def delete_file(file_path: str) -> bool:
    """Delete a file safely."""
    try:
        if os.path.exists(file_path):
            os.remove(file_path)
            return True
        return False
    except Exception as e:
        print(f"Failed to delete file {file_path}: {e}")
        return False

def get_file_info(file_path: str) -> Optional[dict]:
    """Get file information."""
    try:
        if not os.path.exists(file_path):
            return None
        
        stat = os.stat(file_path)
        return {
            "size": stat.st_size,
            "created": stat.st_ctime,
            "modified": stat.st_mtime,
            "exists": True
        }
    except Exception:
        return None
