"""
Chat functionality with WebSocket support for real-time messaging.
"""
import json
import uuid
from datetime import datetime
from typing import Dict, List, Optional
from fastapi import WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session
import logging

from models import User, ChatSession, ChatMessage
from database import get_db

logger = logging.getLogger(__name__)

class ConnectionManager:
    """Manages WebSocket connections for real-time chat."""
    
    def __init__(self):
        # Store active connections: {user_id: {session_id: websocket}}
        self.active_connections: Dict[str, Dict[str, WebSocket]] = {}
        # Store user sessions: {websocket: {"user_id": str, "session_id": str}}
        self.connection_info: Dict[WebSocket, Dict[str, str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str, session_id: str):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        
        # Initialize user connections if not exists
        if user_id not in self.active_connections:
            self.active_connections[user_id] = {}
        
        # Store the connection
        self.active_connections[user_id][session_id] = websocket
        self.connection_info[websocket] = {"user_id": user_id, "session_id": session_id}
        
        logger.info(f"User {user_id} connected to session {session_id}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.connection_info:
            info = self.connection_info[websocket]
            user_id = info["user_id"]
            session_id = info["session_id"]
            
            # Remove from active connections
            if user_id in self.active_connections:
                if session_id in self.active_connections[user_id]:
                    del self.active_connections[user_id][session_id]
                
                # Clean up empty user entry
                if not self.active_connections[user_id]:
                    del self.active_connections[user_id]
            
            # Remove connection info
            del self.connection_info[websocket]
            
            logger.info(f"User {user_id} disconnected from session {session_id}")
    
    async def send_personal_message(self, message: dict, websocket: WebSocket):
        """Send a message to a specific WebSocket connection."""
        try:
            await websocket.send_text(json.dumps(message))
        except Exception as e:
            logger.error(f"Error sending message to websocket: {e}")
    
    async def send_to_session(self, message: dict, user_id: str, session_id: str):
        """Send a message to all connections for a specific session."""
        if user_id in self.active_connections:
            if session_id in self.active_connections[user_id]:
                websocket = self.active_connections[user_id][session_id]
                await self.send_personal_message(message, websocket)
    
    async def broadcast_to_user(self, message: dict, user_id: str):
        """Send a message to all sessions of a specific user."""
        if user_id in self.active_connections:
            for session_id, websocket in self.active_connections[user_id].items():
                await self.send_personal_message(message, websocket)

# Global connection manager instance
manager = ConnectionManager()

def create_chat_session(user_id: str, title: Optional[str] = None, db: Session = None) -> ChatSession:
    """Create a new chat session."""
    session = ChatSession(
        user_id=user_id,
        title=title or f"Chat Session {datetime.now().strftime('%Y-%m-%d %H:%M')}"
    )
    
    if db:
        db.add(session)
        db.commit()
        db.refresh(session)
    
    return session

def save_chat_message(
    session_id: str,
    user_id: str,
    content: str,
    is_user: bool,
    message_type: str = "text",
    db: Session = None
) -> ChatMessage:
    """Save a chat message to the database."""
    message = ChatMessage(
        session_id=session_id,
        user_id=user_id,
        content=content,
        is_user=is_user,
        message_type=message_type
    )
    
    if db:
        db.add(message)
        db.commit()
        db.refresh(message)
        
        # Update session last activity
        session = db.query(ChatSession).filter(ChatSession.id == session_id).first()
        if session:
            session.last_activity = datetime.utcnow()
            db.commit()
    
    return message

def get_chat_history(session_id: str, limit: int = 50, db: Session = None) -> List[ChatMessage]:
    """Get chat history for a session."""
    if not db:
        return []
    
    messages = db.query(ChatMessage).filter(
        ChatMessage.session_id == session_id
    ).order_by(ChatMessage.created_at.desc()).limit(limit).all()
    
    return list(reversed(messages))  # Return in chronological order

def get_user_chat_sessions(user_id: str, db: Session = None) -> List[ChatSession]:
    """Get all chat sessions for a user."""
    if not db:
        return []
    
    sessions = db.query(ChatSession).filter(
        ChatSession.user_id == user_id,
        ChatSession.is_active == True
    ).order_by(ChatSession.last_activity.desc()).all()
    
    return sessions

def delete_chat_session(session_id: str, user_id: str, db: Session = None) -> bool:
    """Delete a chat session (soft delete)."""
    if not db:
        return False
    
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == user_id
    ).first()
    
    if session:
        session.is_active = False
        db.commit()
        return True
    
    return False

def generate_ai_response(message: str, chat_history: List[ChatMessage] = None) -> str:
    """
    Generate an AI response to a user message.
    This is a simple implementation - you can integrate with OpenAI, Claude, or other AI services.
    """
    # Simple rule-based responses for demonstration
    message_lower = message.lower()
    
    if "hello" in message_lower or "hi" in message_lower:
        return "Hello! How can I help you today?"
    elif "how are you" in message_lower:
        return "I'm doing well, thank you for asking! How are you?"
    elif "weather" in message_lower:
        return "I don't have access to real-time weather data, but I'd recommend checking a weather app or website for current conditions."
    elif "time" in message_lower:
        return f"The current time is {datetime.now().strftime('%H:%M:%S')}."
    elif "help" in message_lower:
        return "I'm here to help! You can ask me questions about various topics, and I'll do my best to provide helpful responses."
    elif "bye" in message_lower or "goodbye" in message_lower:
        return "Goodbye! Have a great day!"
    else:
        return "That's an interesting question! I'm a simple AI assistant, so my responses are limited, but I'm here to help with basic conversations and information."

async def handle_websocket_message(websocket: WebSocket, data: dict, db: Session):
    """Handle incoming WebSocket messages."""
    try:
        message_type = data.get("type")
        
        if message_type == "chat_message":
            # Get connection info
            if websocket not in manager.connection_info:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Connection not properly initialized"
                }))
                return
            
            info = manager.connection_info[websocket]
            user_id = info["user_id"]
            session_id = info["session_id"]
            content = data.get("content", "")
            
            if not content.strip():
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Message content cannot be empty"
                }))
                return
            
            # Save user message
            user_message = save_chat_message(
                session_id=session_id,
                user_id=user_id,
                content=content,
                is_user=True,
                db=db
            )
            
            # Send confirmation to user
            await websocket.send_text(json.dumps({
                "type": "message_sent",
                "message": {
                    "id": user_message.id,
                    "content": user_message.content,
                    "is_user": True,
                    "created_at": user_message.created_at.isoformat(),
                    "message_type": user_message.message_type
                }
            }))
            
            # Generate AI response
            chat_history = get_chat_history(session_id, limit=10, db=db)
            ai_response_content = generate_ai_response(content, chat_history)
            
            # Save AI response
            ai_message = save_chat_message(
                session_id=session_id,
                user_id=user_id,
                content=ai_response_content,
                is_user=False,
                db=db
            )
            
            # Send AI response to user
            await websocket.send_text(json.dumps({
                "type": "ai_response",
                "message": {
                    "id": ai_message.id,
                    "content": ai_message.content,
                    "is_user": False,
                    "created_at": ai_message.created_at.isoformat(),
                    "message_type": ai_message.message_type
                }
            }))
            
        elif message_type == "ping":
            # Handle ping for connection keep-alive
            await websocket.send_text(json.dumps({"type": "pong"}))
            
        else:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            }))
            
    except Exception as e:
        logger.error(f"Error handling WebSocket message: {e}")
        await websocket.send_text(json.dumps({
            "type": "error",
            "message": "An error occurred while processing your message"
        }))
