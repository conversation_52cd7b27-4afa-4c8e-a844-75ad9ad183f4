# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1

# Authentication and Security
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0
python-multipart==0.0.6

# Email
python-dotenv==1.0.0

# File handling and image processing
Pillow==10.1.0
aiofiles==23.2.1

# Validation
pydantic[email]==2.5.0

# WebSocket support (included with FastAPI/uvicorn)

# Development and testing
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# Optional: For production deployment
gunicorn==21.2.0
