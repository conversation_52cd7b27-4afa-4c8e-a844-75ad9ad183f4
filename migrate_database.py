"""
Database migration script to update schema for dual token system.
"""
import sqlite3
import os
from datetime import datetime, timedelta
from config import settings

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table."""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [column[1] for column in cursor.fetchall()]
    return column_name in columns

def migrate_user_sessions_table():
    """Migrate user_sessions table to support dual token system."""
    db_path = settings.DATABASE_URL.replace("sqlite:///", "").replace("./", "")
    
    print(f"Migrating database: {db_path}")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if the table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='user_sessions'
        """)
        
        if not cursor.fetchone():
            print("user_sessions table doesn't exist. Creating new table with correct schema.")
            # Table doesn't exist, create it with the new schema
            cursor.execute("""
                CREATE TABLE user_sessions (
                    id VARCHAR PRIMARY KEY,
                    user_id VARCHAR NOT NULL,
                    session_token VARCHAR UNIQUE NOT NULL,
                    access_token TEXT NOT NULL,
                    refresh_token VARCHAR UNIQUE NOT NULL,
                    ip_address VARCHAR,
                    user_agent VARCHAR,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    access_expires_at DATETIME NOT NULL,
                    refresh_expires_at DATETIME NOT NULL,
                    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY(user_id) REFERENCES users (id)
                )
            """)
            print("Created user_sessions table with new schema.")
            conn.commit()
            return
        
        # Check if we need to migrate
        has_old_expires_at = check_column_exists(cursor, 'user_sessions', 'expires_at')
        has_access_expires_at = check_column_exists(cursor, 'user_sessions', 'access_expires_at')
        has_refresh_expires_at = check_column_exists(cursor, 'user_sessions', 'refresh_expires_at')
        has_access_token = check_column_exists(cursor, 'user_sessions', 'access_token')
        has_refresh_token = check_column_exists(cursor, 'user_sessions', 'refresh_token')
        has_jwt_token = check_column_exists(cursor, 'user_sessions', 'jwt_token')
        
        print(f"Schema check:")
        print(f"  - has_old_expires_at: {has_old_expires_at}")
        print(f"  - has_access_expires_at: {has_access_expires_at}")
        print(f"  - has_refresh_expires_at: {has_refresh_expires_at}")
        print(f"  - has_access_token: {has_access_token}")
        print(f"  - has_refresh_token: {has_refresh_token}")
        print(f"  - has_jwt_token: {has_jwt_token}")
        
        # If we already have the new schema, no migration needed
        if has_access_expires_at and has_refresh_expires_at and has_access_token and has_refresh_token:
            print("Database schema is already up to date.")
            return
        
        print("Starting migration...")
        
        # Create backup table name
        backup_table = f"user_sessions_backup_{int(datetime.now().timestamp())}"
        
        # Rename existing table to backup
        cursor.execute(f"ALTER TABLE user_sessions RENAME TO {backup_table}")
        print(f"Backed up existing table to {backup_table}")
        
        # Create new table with correct schema
        cursor.execute("""
            CREATE TABLE user_sessions (
                id VARCHAR PRIMARY KEY,
                user_id VARCHAR NOT NULL,
                session_token VARCHAR UNIQUE NOT NULL,
                access_token TEXT NOT NULL,
                refresh_token VARCHAR UNIQUE NOT NULL,
                ip_address VARCHAR,
                user_agent VARCHAR,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                access_expires_at DATETIME NOT NULL,
                refresh_expires_at DATETIME NOT NULL,
                last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY(user_id) REFERENCES users (id)
            )
        """)
        print("Created new user_sessions table with updated schema.")
        
        # Check if there's data to migrate
        cursor.execute(f"SELECT COUNT(*) FROM {backup_table}")
        count = cursor.fetchone()[0]
        
        if count > 0:
            print(f"Found {count} existing sessions. Clearing them for security (users will need to re-login).")
            # For security, we'll clear existing sessions rather than try to migrate them
            # This ensures all sessions use the new dual token system
            print("Existing sessions cleared. Users will need to sign in again.")
        
        # Drop the backup table since we're not migrating data
        cursor.execute(f"DROP TABLE {backup_table}")
        print("Removed backup table.")
        
        conn.commit()
        print("Migration completed successfully!")
        
    except Exception as e:
        print(f"Migration failed: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def verify_migration():
    """Verify that the migration was successful."""
    db_path = settings.DATABASE_URL.replace("sqlite:///", "").replace("./", "")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check table structure
        cursor.execute("PRAGMA table_info(user_sessions)")
        columns = cursor.fetchall()
        
        print("\nVerification - user_sessions table structure:")
        for column in columns:
            print(f"  {column[1]} {column[2]} {'NOT NULL' if column[3] else 'NULL'}")
        
        # Check for required columns
        column_names = [column[1] for column in columns]
        required_columns = [
            'id', 'user_id', 'session_token', 'access_token', 'refresh_token',
            'access_expires_at', 'refresh_expires_at', 'created_at', 'last_activity', 'is_active'
        ]
        
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        else:
            print("✅ All required columns present.")
            return True
            
    except Exception as e:
        print(f"Verification failed: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("Starting database migration for dual token system...")
    migrate_user_sessions_table()
    
    print("\nVerifying migration...")
    if verify_migration():
        print("\n✅ Database migration completed successfully!")
        print("The application should now start without schema errors.")
    else:
        print("\n❌ Migration verification failed!")
