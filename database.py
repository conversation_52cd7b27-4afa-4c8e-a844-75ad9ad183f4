"""
Database configuration and session management.
"""
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from config import settings

# Create database engine
engine = create_engine(
    settings.DATABASE_URL,
    connect_args=settings.database_connect_args
)

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create base class for models
Base = declarative_base()

def get_db() -> Session:
    """
    Dependency to get a database session.
    This function will be used as a FastAPI dependency.
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def init_database():
    """Initialize database tables."""
    # Import all models to ensure they are registered with SQLAlchemy
    from models import User, PendingVerification, PasswordResetToken, ChatSession, ChatMessage, UserSession

    # Create all tables
    Base.metadata.create_all(bind=engine)
    print("Database tables created successfully!")

    # Check if migration is needed for existing databases
    try:
        from migrate_database import verify_migration
        if not verify_migration():
            print("Warning: Database schema may need migration. Run 'python migrate_database.py' if you encounter errors.")
    except Exception as e:
        print(f"Could not verify migration status: {e}")

if __name__ == "__main__":
    init_database()
