'use client'

import './globals.css'
import { Inter } from 'next/font/google'
import { AuthProvider } from '../auth/authContext'

const inter = Inter({ subsets: ['latin'] })

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <head>
        <title>Athlix - Premium Chat Platform</title>
        <meta name="description" content="Athlix - Your premium chat platform with secure authentication and real-time messaging" />
      </head>
      <body className={inter.className}>
        <AuthProvider>
          <div className="min-h-screen bg-gray-50">
            {children}
          </div>
        </AuthProvider>
      </body>
    </html>
  )
}