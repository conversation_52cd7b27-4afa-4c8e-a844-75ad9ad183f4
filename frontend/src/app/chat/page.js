'use client'

import { useState, useEffect, useRef } from 'react'
import { useAuth } from '../../auth/authContext'
import { useRouter } from 'next/navigation'

export default function ChatPage() {
  const { user, token, logout, authenticatedFetch } = useAuth()
  const router = useRouter()
  const [messages, setMessages] = useState([])
  const [inputMessage, setInputMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [sessions, setSessions] = useState([])
  const [currentSessionId, setCurrentSessionId] = useState(null)
  const [ws, setWs] = useState(null)
  const [isConnected, setIsConnected] = useState(false)
  const [connectionError, setConnectionError] = useState(null)
  const [reconnectAttempts, setReconnectAttempts] = useState(0)
  const [selectedImage, setSelectedImage] = useState(null)
  const [imagePreview, setImagePreview] = useState(null)
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const messagesEndRef = useRef(null)
  const reconnectTimeoutRef = useRef(null)
  const fileInputRef = useRef(null)

  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  const handleImageSelect = (event) => {
    const file = event.target.files[0]
    if (file) {
      if (file.size > 5 * 1024 * 1024) { // 5MB limit
        alert('Image size must be less than 5MB')
        return
      }

      if (!file.type.startsWith('image/')) {
        alert('Please select an image file')
        return
      }

      setSelectedImage(file)

      // Create preview
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    setSelectedImage(null)
    setImagePreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const filteredSessions = sessions.filter(session =>
    session.title.toLowerCase().includes(searchTerm.toLowerCase())
  )

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    if (!user) {
      router.push('/signin')
      return
    }
    
    // Load chat sessions
    loadChatSessions()
  }, [user, router])

  useEffect(() => {
    if (currentSessionId && token) {
      connectWebSocket()
    }
    
    return () => {
      if (ws) {
        ws.close()
      }
    }
  }, [currentSessionId, token])

  const loadChatSessions = async () => {
    try {
      const response = await authenticatedFetch(`${API_BASE_URL}/chat/sessions`)

      if (response.ok) {
        const sessionsData = await response.json()
        setSessions(sessionsData)

        // If no current session and sessions exist, select the first one
        if (!currentSessionId && sessionsData.length > 0) {
          setCurrentSessionId(sessionsData[0].id)
        }
      } else {
        console.error('Failed to load chat sessions:', response.status)
      }
    } catch (error) {
      console.error('Error loading chat sessions:', error)
    }
  }

  const createNewSession = async () => {
    try {
      const response = await authenticatedFetch(`${API_BASE_URL}/chat/sessions`, {
        method: 'POST',
        body: JSON.stringify({
          title: `Chat ${new Date().toLocaleString()}`
        }),
      })

      if (response.ok) {
        const newSession = await response.json()
        setSessions(prev => [newSession, ...prev])
        setCurrentSessionId(newSession.id)
        setMessages([])
      } else {
        console.error('Failed to create new session:', response.status)
      }
    } catch (error) {
      console.error('Error creating new session:', error)
    }
  }

  const loadSessionMessages = async (sessionId) => {
    try {
      const response = await authenticatedFetch(`${API_BASE_URL}/chat/sessions/${sessionId}/messages`)

      if (response.ok) {
        const messagesData = await response.json()
        setMessages(messagesData.map(msg => ({
          id: msg.id,
          content: msg.content,
          is_user: msg.is_user,
          created_at: msg.created_at,
          message_type: msg.message_type
        })))
      } else {
        console.error('Failed to load session messages:', response.status)
      }
    } catch (error) {
      console.error('Error loading session messages:', error)
    }
  }

  const connectWebSocket = () => {
    if (ws) {
      ws.close()
    }

    // Clear any existing reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current)
    }

    setConnectionError(null)
    const wsUrl = `ws://localhost:8000/ws/chat/${currentSessionId}?token=${token}`
    const websocket = new WebSocket(wsUrl)

    websocket.onopen = () => {
      console.log('WebSocket connected')
      setIsConnected(true)
      setConnectionError(null)
      setReconnectAttempts(0)
      loadSessionMessages(currentSessionId)
    }

    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)

        if (data.type === 'message_sent' || data.type === 'ai_response') {
          const newMessage = {
            id: data.message.id,
            content: data.message.content,
            is_user: data.message.is_user,
            created_at: data.message.created_at,
            message_type: data.message.message_type
          }

          setMessages(prev => [...prev, newMessage])
        } else if (data.type === 'error') {
          console.error('WebSocket error:', data.message)
          setConnectionError(data.message)
        } else if (data.type === 'connected') {
          console.log('WebSocket connection confirmed:', data.message)
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error)
      }
    }

    websocket.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason)
      setIsConnected(false)

      // Attempt to reconnect if not a normal closure
      if (event.code !== 1000 && reconnectAttempts < 5) {
        const timeout = Math.min(1000 * Math.pow(2, reconnectAttempts), 30000) // Exponential backoff
        setConnectionError(`Connection lost. Reconnecting in ${timeout/1000}s...`)

        reconnectTimeoutRef.current = setTimeout(() => {
          setReconnectAttempts(prev => prev + 1)
          connectWebSocket()
        }, timeout)
      } else if (reconnectAttempts >= 5) {
        setConnectionError('Connection failed. Please refresh the page.')
      }
    }

    websocket.onerror = (error) => {
      console.error('WebSocket error:', error)
      setIsConnected(false)
      setConnectionError('Connection error occurred')
    }

    setWs(websocket)
  }

  const sendMessage = async () => {
    if ((!inputMessage.trim() && !selectedImage) || isLoading || !ws || !isConnected) return

    setIsLoading(true)

    try {
      let messageContent = inputMessage.trim()
      let imageData = null

      // Handle image upload
      if (selectedImage) {
        try {
          const formData = new FormData()
          formData.append('image', selectedImage)
          formData.append('session_id', currentSessionId)

          const response = await authenticatedFetch(`${API_BASE_URL}/chat/upload-image`, {
            method: 'POST',
            body: formData,
          })

          if (response.ok) {
            const result = await response.json()
            imageData = result.image_url
          } else {
            console.error('Failed to upload image')
            return
          }
        } catch (error) {
          console.error('Error uploading image:', error)
          return
        }
      }

      ws.send(JSON.stringify({
        type: 'chat_message',
        content: messageContent || 'Image',
        image_url: imageData
      }))

      setInputMessage('')
      removeImage()
    } catch (error) {
      console.error('Error sending message:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const selectSession = (sessionId) => {
    setCurrentSessionId(sessionId)
    setMessages([])
  }

  if (!user) {
    return <div>Loading...</div>
  }

  return (
    <div className="flex h-screen bg-gray-100">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-80' : 'w-16'} bg-white border-r border-gray-200 flex flex-col transition-all duration-300`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex justify-between items-center mb-4">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 hover:bg-gray-100 rounded-lg"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            {sidebarOpen && (
              <>
                <h2 className="text-lg font-semibold text-gray-800">Athlix Chat</h2>
                <button
                  onClick={createNewSession}
                  className="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600 flex items-center space-x-1"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span>New</span>
                </button>
              </>
            )}
          </div>

          {sidebarOpen && (
            <>
              {/* Search */}
              <div className="mb-4">
                <input
                  type="text"
                  placeholder="Search chats..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Connection Status */}
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  isConnected ? 'bg-green-500' :
                  connectionError ? 'bg-red-500' : 'bg-yellow-500'
                }`}></div>
                <span className="text-sm text-gray-600">
                  {isConnected ? 'Connected' :
                   connectionError ? connectionError : 'Connecting...'}
                </span>
              </div>
            </>
          )}
        </div>

        {/* Sessions List */}
        <div className="flex-1 overflow-y-auto">
          {sidebarOpen ? (
            filteredSessions.length > 0 ? (
              filteredSessions.map((session) => (
                <div
                  key={session.id}
                  onClick={() => selectSession(session.id)}
                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${
                    currentSessionId === session.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                  }`}
                >
                  <h3 className="font-medium text-gray-800 truncate">{session.title}</h3>
                  <p className="text-sm text-gray-500 mt-1">
                    {new Date(session.last_activity).toLocaleDateString()}
                  </p>
                  {session.message_count && (
                    <p className="text-xs text-gray-400 mt-1">
                      {session.message_count} messages
                    </p>
                  )}
                </div>
              ))
            ) : (
              <div className="p-4 text-center text-gray-500">
                {searchTerm ? 'No chats found' : 'No chat sessions yet'}
              </div>
            )
          ) : (
            // Collapsed sidebar - show only icons
            sessions.slice(0, 5).map((session, index) => (
              <div
                key={session.id}
                onClick={() => selectSession(session.id)}
                className={`p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 flex justify-center ${
                  currentSessionId === session.id ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                }`}
                title={session.title}
              >
                <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-sm font-medium">
                  {index + 1}
                </div>
              </div>
            ))
          )}
        </div>

        {/* User Info */}
        <div className="p-4 border-t border-gray-200">
          {sidebarOpen ? (
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-medium">
                  {user.first_name?.[0]}{user.last_name?.[0]}
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-800">{user.first_name} {user.last_name}</p>
                  <p className="text-xs text-gray-500">{user.email}</p>
                </div>
              </div>
              <button
                onClick={logout}
                className="text-sm text-red-600 hover:text-red-800 p-2 hover:bg-red-50 rounded"
                title="Logout"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </div>
          ) : (
            <div className="flex justify-center">
              <button
                onClick={logout}
                className="w-8 h-8 bg-red-100 text-red-600 rounded-full flex items-center justify-center hover:bg-red-200"
                title="Logout"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="bg-white shadow-sm border-b px-6 py-4">
          <div className="flex justify-between items-center">
            <h1 className="text-xl font-semibold text-gray-800">
              {currentSessionId ? 
                sessions.find(s => s.id === currentSessionId)?.title || 'Chat' : 
                'Select a chat session'
              }
            </h1>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-6 space-y-4">
          {!currentSessionId ? (
            <div className="text-center text-gray-500 mt-8">
              <p>Select a chat session or create a new one to start chatting!</p>
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center text-gray-500 mt-8">
              <p>No messages yet. Start a conversation!</p>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.is_user ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                    message.is_user
                      ? 'bg-blue-500 text-white'
                      : 'bg-white text-gray-800 border'
                  }`}
                >
                  <p className="text-sm whitespace-pre-wrap">{message.content}</p>
                  <p className="text-xs mt-1 opacity-70">
                    {new Date(message.created_at).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))
          )}
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-white text-gray-800 border max-w-xs lg:max-w-md px-4 py-2 rounded-lg">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                </div>
              </div>
            </div>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        {currentSessionId && (
          <div className="bg-white border-t px-6 py-4">
            <div className="flex space-x-4">
              <textarea
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="flex-1 border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                rows="1"
                disabled={isLoading || !isConnected}
              />
              <button
                onClick={sendMessage}
                disabled={!inputMessage.trim() || isLoading || !isConnected}
                className="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Send
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
