'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'

const AuthContext = createContext({})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [token, setToken] = useState(null)
  const [refreshToken, setRefreshToken] = useState(null)
  const [tokenRefreshTimer, setTokenRefreshTimer] = useState(null)
  const router = useRouter()

  // API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'

  // Initialize auth state
  useEffect(() => {
    initializeAuth()
  }, [])

  const initializeAuth = async () => {
    try {
      const storedToken = sessionStorage.getItem('access_token')
      const storedRefreshToken = sessionStorage.getItem('refresh_token')

      if (storedToken && storedRefreshToken) {
        setToken(storedToken)
        setRefreshToken(storedRefreshToken)

        // Try to fetch user profile, refresh token if needed
        const success = await fetchUserProfile(storedToken)
        if (!success) {
          // Try to refresh the token
          await attemptTokenRefresh(storedRefreshToken)
        } else {
          // Set up automatic token refresh
          setupTokenRefresh()
        }
      }
    } catch (error) {
      console.error('Auth initialization error:', error)
      logout()
    } finally {
      setLoading(false)
    }
  }

  const setupTokenRefresh = () => {
    // Clear existing timer
    if (tokenRefreshTimer) {
      clearTimeout(tokenRefreshTimer)
    }

    // Set up refresh 5 minutes before expiration (JWT expires in 24 hours by default)
    const refreshTime = (24 * 60 - 5) * 60 * 1000; // 23 hours 55 minutes in milliseconds
    const timer = setTimeout(() => {
      attemptTokenRefresh(refreshToken)
    }, refreshTime)

    setTokenRefreshTimer(timer)
  }

  const attemptTokenRefresh = async (currentRefreshToken) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: currentRefreshToken }),
      })

      if (response.ok) {
        const data = await response.json()
        const { access_token, refresh_token, user } = data

        // Update tokens
        setToken(access_token)
        setRefreshToken(refresh_token)
        setUser(user)

        // Store in session storage
        sessionStorage.setItem('access_token', access_token)
        sessionStorage.setItem('refresh_token', refresh_token)

        // Setup next refresh
        setupTokenRefresh()

        return true
      } else {
        // Refresh failed, logout user
        logout()
        return false
      }
    } catch (error) {
      console.error('Token refresh error:', error)
      logout()
      return false
    }
  }

  const fetchUserProfile = async (authToken) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (response.ok) {
        const userData = await response.json()
        setUser(userData)
        return true
      } else if (response.status === 401) {
        // Token expired, try to refresh
        return false
      } else {
        throw new Error('Failed to fetch user profile')
      }
    } catch (error) {
      console.error('Failed to fetch user profile:', error)
      return false
    }
  }

  const login = async (email, password) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/signin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (response.ok) {
        const { access_token, refresh_token, user } = data
        setToken(access_token)
        setRefreshToken(refresh_token)
        setUser(user)

        // Store tokens
        sessionStorage.setItem('access_token', access_token)
        sessionStorage.setItem('refresh_token', refresh_token)

        // Setup automatic token refresh
        setupTokenRefresh()

        return { success: true, user }
      } else {
        return {
          success: false,
          error: data.detail || 'Authentication failed'
        }
      }
    } catch (error) {
      console.error('Login error:', error)
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      }
    }
  }

  const signup = async (userData) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/signup`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      })

      const data = await response.json()

      if (response.ok) {
        return {
          success: true,
          message: data.message,
          userId: data.user_id
        }
      } else {
        return {
          success: false,
          error: data.detail || 'Signup failed'
        }
      }
    } catch (error) {
      console.error('Signup error:', error)
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      }
    }
  }

  const verifyEmail = async (email, verification_code) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/verify-email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, verification_code }),
      })

      const data = await response.json()

      if (response.ok) {
        return { success: true, message: data.message }
      } else {
        return {
          success: false,
          error: data.detail || 'Email verification failed'
        }
      }
    } catch (error) {
      console.error('Email verification error:', error)
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      }
    }
  }

  const sendOTP = async (email, purpose = 'login') => {
    try {
      const endpoint = purpose === 'password_reset' ? '/auth/forgot-password' :
                     purpose === 'verification' ? '/auth/resend-verification' : '/auth/send-otp'
      
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      })

      const data = await response.json()

      if (response.ok) {
        return { 
          success: true, 
          message: data.message,
          expiresInMinutes: data.expires_in_minutes 
        }
      } else {
        return { 
          success: false, 
          error: data.detail || 'Failed to send OTP' 
        }
      }
    } catch (error) {
      console.error('Send OTP error:', error)
      return { 
        success: false, 
        error: 'Network error. Please check your connection.' 
      }
    }
  }

  const resetPassword = async (email, reset_code, new_password) => {
    try {
      const response = await fetch(`${API_BASE_URL}/auth/reset-password`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          reset_code,
          new_password
        }),
      })

      const data = await response.json()

      if (response.ok) {
        return { success: true, message: data.message }
      } else {
        return {
          success: false,
          error: data.detail || 'Password reset failed'
        }
      }
    } catch (error) {
      console.error('Password reset error:', error)
      return {
        success: false,
        error: 'Network error. Please check your connection.'
      }
    }
  }

  const logout = async () => {
    try {
      // Call backend logout endpoint if token exists
      if (token) {
        await fetch(`${API_BASE_URL}/auth/logout`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        })
      }
    } catch (error) {
      console.error('Logout error:', error)
      // Continue with local logout even if backend call fails
    } finally {
      // Clear refresh timer
      if (tokenRefreshTimer) {
        clearTimeout(tokenRefreshTimer)
        setTokenRefreshTimer(null)
      }

      // Always clear local state
      setUser(null)
      setToken(null)
      setRefreshToken(null)
      sessionStorage.removeItem('access_token')
      sessionStorage.removeItem('refresh_token')
      sessionStorage.removeItem('token') // Legacy cleanup
      localStorage.removeItem('access_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user')
      router.push('/signin')
    }
  }

  // Helper function for authenticated API calls
  const authenticatedFetch = async (url, options = {}) => {
    const headers = {
      'Content-Type': 'application/json',
      ...options.headers,
    }

    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      })

      // If token expired, try to refresh
      if (response.status === 401 && refreshToken) {
        const refreshSuccess = await attemptTokenRefresh(refreshToken)
        if (refreshSuccess) {
          // Retry the request with new token
          headers.Authorization = `Bearer ${token}`
          return fetch(url, {
            ...options,
            headers,
          })
        }
      }

      return response
    } catch (error) {
      console.error('Authenticated fetch error:', error)
      throw error
    }
  }

  const value = {
    user,
    token,
    refreshToken,
    loading,
    login,
    signup,
    verifyEmail,
    sendOTP,
    resetPassword,
    logout,
    authenticatedFetch,
    isAuthenticated: !!user && !!token
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}