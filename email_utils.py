"""
Email utilities for sending verification and reset emails.
"""
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from threading import Thread
from typing import Optional
import logging

from config import settings

logger = logging.getLogger(__name__)

def send_email(to_email: str, subject: str, html_body: str, text_body: Optional[str] = None):
    """Send an email using SMTP or mock for development."""
    try:
        # Check if email is enabled
        if not settings.EMAIL_ENABLED:
            logger.info(f"📧 Email disabled - Mock sending email to {to_email}")
            logger.info(f"📧 Subject: {subject}")
            # Extract verification code or reset code from HTML for easy testing
            import re
            code_match = re.search(r'(\d{6})', html_body)
            if code_match:
                logger.info(f"🔑 VERIFICATION/RESET CODE: {code_match.group(1)}")
            return

        msg = MIMEMultipart('alternative')
        msg['From'] = settings.EMAIL_ADDRESS
        msg['To'] = to_email
        msg['Subject'] = subject

        # Add text version if provided
        if text_body:
            text_part = MIMEText(text_body, 'plain')
            msg.attach(text_part)

        # Add HTML version
        html_part = MIMEText(html_body, 'html')
        msg.attach(html_part)

        # Connect to server and send email
        server = smtplib.SMTP(settings.SMTP_SERVER, settings.SMTP_PORT)

        # Only use TLS and login if credentials are provided
        if settings.EMAIL_PASSWORD:
            server.starttls()
            server.login(settings.EMAIL_ADDRESS, settings.EMAIL_PASSWORD)

        server.sendmail(settings.EMAIL_ADDRESS, to_email, msg.as_string())
        server.quit()

        logger.info(f"Email sent successfully to {to_email}")

    except Exception as e:
        logger.error(f"Failed to send email to {to_email}: {str(e)}")
        # For development, don't raise the error - just log it and extract code
        logger.info(f"📧 Email sending failed, but continuing (development mode)")
        # Extract verification code for console output
        import re
        code_match = re.search(r'(\d{6})', html_body)
        if code_match:
            logger.info(f"🔑 VERIFICATION/RESET CODE (from failed email): {code_match.group(1)}")
        return

def send_email_background(to_email: str, subject: str, html_body: str, text_body: Optional[str] = None):
    """Send email in background thread."""
    thread = Thread(target=send_email, args=(to_email, subject, html_body, text_body))
    thread.daemon = True
    thread.start()

def create_verification_email_html(verification_code: str, first_name: str) -> str:
    """Create HTML content for verification email."""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Account</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
        <table role="presentation" style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 40px 0; text-align: center;">
                    <table role="presentation" style="width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <!-- Header -->
                        <tr>
                            <td style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 40px 30px; text-align: center; border-radius: 8px 8px 0 0;">
                                <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">{settings.APP_NAME}</h1>
                                <p style="color: #ffffff; margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Welcome! Please verify your account</p>
                            </td>
                        </tr>
                        
                        <!-- Content -->
                        <tr>
                            <td style="padding: 40px 30px;">
                                <h2 style="color: #333333; margin: 0 0 20px 0; font-size: 24px; text-align: center;">Hi {first_name}!</h2>
                                
                                <p style="color: #666666; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0; text-align: center;">
                                    🎉 Welcome to <strong>{settings.APP_NAME}</strong>! We're excited to have you join our premium chat platform.
                                </p>
                                
                                <p style="color: #666666; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
                                    To complete your account setup and ensure the security of your account, please verify your email address using the code below:
                                </p>
                                
                                <!-- Verification Code Box -->
                                <div style="background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%); border: 2px solid #28a745; border-radius: 8px; padding: 25px; text-align: center; margin: 25px 0;">
                                    <p style="margin: 0 0 10px 0; color: #333333; font-size: 14px; font-weight: bold;">Your Verification Code:</p>
                                    <p style="margin: 0; font-size: 36px; font-weight: bold; color: #28a745; letter-spacing: 4px; font-family: 'Courier New', monospace;">{verification_code}</p>
                                </div>
                                
                                <div style="background-color: #fff8e1; border-left: 4px solid #ffc107; padding: 15px; border-radius: 4px; margin: 25px 0;">
                                    <p style="color: #856404; font-size: 14px; margin: 0; font-weight: bold;">
                                        ⏰ Quick Action Required: This code will expire in {settings.OTP_EXPIRATION_MINUTES} minutes for security reasons.
                                    </p>
                                </div>
                                
                                <p style="color: #666666; font-size: 16px; line-height: 1.6; margin: 25px 0 0 0;">
                                    If you didn't create an account with {settings.APP_NAME}, please ignore this email. No further action is required.
                                </p>
                            </td>
                        </tr>
                        
                        <!-- Footer -->
                        <tr>
                            <td style="background-color: #f8f9fa; padding: 30px; text-align: center; border-radius: 0 0 8px 8px; border-top: 1px solid #e9ecef;">
                                <p style="margin: 0 0 10px 0; color: #333333; font-size: 16px; font-weight: bold;">Welcome to the team!</p>
                                <p style="margin: 0; color: #667eea; font-size: 18px; font-weight: bold;">The {settings.APP_NAME} Team</p>
                                
                                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                                    <p style="margin: 0; color: #999999; font-size: 12px;">
                                        This is an automated message. Please do not reply to this email.
                                    </p>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """

def create_password_reset_email_html(reset_code: str, first_name: str) -> str:
    """Create HTML content for password reset email."""
    return f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Password Reset Request</title>
    </head>
    <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f4f4f4;">
        <table role="presentation" style="width: 100%; border-collapse: collapse;">
            <tr>
                <td style="padding: 40px 0; text-align: center;">
                    <table role="presentation" style="width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
                        <!-- Header -->
                        <tr>
                            <td style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); padding: 40px 30px; text-align: center; border-radius: 8px 8px 0 0;">
                                <h1 style="color: #ffffff; margin: 0; font-size: 28px; font-weight: bold;">{settings.APP_NAME}</h1>
                                <p style="color: #ffffff; margin: 10px 0 0 0; font-size: 16px; opacity: 0.9;">Password Reset Request</p>
                            </td>
                        </tr>
                        
                        <!-- Content -->
                        <tr>
                            <td style="padding: 40px 30px;">
                                <h2 style="color: #333333; margin: 0 0 20px 0; font-size: 24px;">Hi {first_name},</h2>
                                
                                <p style="color: #666666; font-size: 16px; line-height: 1.6; margin: 0 0 25px 0;">
                                    We received a request to reset your password for your {settings.APP_NAME} account. Use the code below to reset your password:
                                </p>
                                
                                <!-- Reset Code Box -->
                                <div style="background-color: #f8f9fa; border: 2px dashed #dc3545; border-radius: 8px; padding: 20px; text-align: center; margin: 25px 0;">
                                    <p style="margin: 0 0 10px 0; color: #333333; font-size: 14px; font-weight: bold;">Your Reset Code:</p>
                                    <p style="margin: 0; font-size: 32px; font-weight: bold; color: #dc3545; letter-spacing: 3px; font-family: 'Courier New', monospace;">{reset_code}</p>
                                </div>
                                
                                <p style="color: #666666; font-size: 16px; line-height: 1.6; margin: 25px 0;">
                                    <strong>Important:</strong> This code will expire in {settings.RESET_TOKEN_EXPIRATION_MINUTES} minutes for security reasons.
                                </p>
                                
                                <p style="color: #666666; font-size: 16px; line-height: 1.6; margin: 25px 0 0 0;">
                                    If you didn't request a password reset, please ignore this email. Your account remains secure.
                                </p>
                            </td>
                        </tr>
                        
                        <!-- Footer -->
                        <tr>
                            <td style="background-color: #f8f9fa; padding: 30px; text-align: center; border-radius: 0 0 8px 8px; border-top: 1px solid #e9ecef;">
                                <p style="margin: 0 0 10px 0; color: #333333; font-size: 16px; font-weight: bold;">Best regards,</p>
                                <p style="margin: 0; color: #dc3545; font-size: 18px; font-weight: bold;">The {settings.APP_NAME} Team</p>
                                
                                <div style="margin-top: 20px; padding-top: 20px; border-top: 1px solid #e9ecef;">
                                    <p style="margin: 0; color: #999999; font-size: 12px;">
                                        This is an automated message. Please do not reply to this email.
                                    </p>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </body>
    </html>
    """
