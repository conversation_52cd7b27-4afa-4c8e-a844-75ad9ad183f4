# Athlix - Premium Chat Platform

A comprehensive full-stack web application featuring secure user authentication, real-time chat functionality, and modern user experience. Built with FastAPI (Python) backend and Next.js (React) frontend.

## Features

### 🔐 Advanced Authentication System
- **User Registration** with email verification
- **OTP (One-Time Password)** verification for account activation
- **Password Reset** functionality with secure reset codes
- **Dual Token System** - Access tokens with automatic refresh tokens
- **Automatic Token Refresh** - Seamless session management
- **Account Management** (profile updates, account deletion)
- **Session Control** with multiple device support
- **Token Expiration Handling** with automatic re-authentication

### 💬 Enhanced Real-time Chat System
- **WebSocket-powered** real-time messaging with authentication
- **Chat Sessions** management (create, view, delete)
- **Message History** storage and retrieval
- **AI-powered** chat responses (simple rule-based for demo)
- **Session Persistence** across browser sessions
- **Connection Status** indicators and error handling
- **Automatic Reconnection** with exponential backoff
- **Real-time Message Delivery** with delivery confirmation

### 🛡️ Security Features
- **Password Hashing** with bcrypt
- **Input Validation** and sanitization
- **Rate Limiting** protection
- **CORS** configuration
- **Session Management** with automatic cleanup
- **Secure Token** handling

### 🎨 Modern UI/UX
- **Responsive Design** with Tailwind CSS
- **Real-time Updates** and notifications
- **Loading States** and error handling
- **Clean Interface** with intuitive navigation
- **Mobile-friendly** responsive layout

## Technology Stack

### Backend
- **FastAPI** - Modern Python web framework
- **SQLAlchemy** - Database ORM
- **SQLite** - Database (easily configurable for PostgreSQL/MySQL)
- **Pydantic** - Data validation
- **JWT** - Authentication tokens
- **WebSockets** - Real-time communication
- **SMTP** - Email sending

### Frontend
- **Next.js 15** - React framework
- **React 19** - UI library
- **Tailwind CSS** - Styling
- **WebSocket API** - Real-time communication
- **Context API** - State management

## Quick Start

### Prerequisites
- Python 3.8+
- Node.js 18+
- npm or yarn

### Backend Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd <project-directory>
   ```

2. **Create virtual environment**
   ```bash
   python -m venv env
   source env/bin/activate  # On Windows: env\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env file with your configuration
   ```

5. **Initialize database**
   ```bash
   python database.py
   ```

6. **Run the backend server**
   ```bash
   uvicorn app:app --reload --host 0.0.0.0 --port 8000
   ```

### Frontend Setup

1. **Navigate to frontend directory**
   ```bash
   cd frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   # Create .env.local file
   echo "NEXT_PUBLIC_API_URL=http://localhost:8000" > .env.local
   ```

4. **Run the frontend server**
   ```bash
   npm run dev
   ```

### Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs

## Configuration

### Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Database Configuration
DATABASE_URL="sqlite:///./app_database.db"

# JWT Configuration
JWT_SECRET_KEY="your-super-secret-jwt-key-change-this-in-production"
JWT_ALGORITHM="HS256"
JWT_EXPIRATION_HOURS=24

# Email Configuration (SMTP)
SMTP_SERVER="smtp.gmail.com"
SMTP_PORT=587
EMAIL_ADDRESS="<EMAIL>"
EMAIL_PASSWORD="your-app-password"

# OTP Configuration
OTP_EXPIRATION_MINUTES=15
RESET_TOKEN_EXPIRATION_MINUTES=30

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"

# Application Configuration
APP_NAME="Full-Stack Chat App"
APP_VERSION="1.0.0"
DEBUG=true

# Session Configuration
SESSION_SECRET_KEY="your-session-secret-key-change-this"
SESSION_EXPIRATION_HOURS=168
```

### Email Setup

For email functionality, you'll need to configure SMTP settings:

1. **Gmail Setup** (recommended for development):
   - Enable 2-factor authentication
   - Generate an App Password
   - Use the App Password in `EMAIL_PASSWORD`

2. **Other SMTP Providers**:
   - Update `SMTP_SERVER` and `SMTP_PORT`
   - Provide appropriate credentials

## API Documentation

The API is automatically documented with FastAPI's built-in Swagger UI:
- **Interactive Docs**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

### Key Endpoints

#### Authentication
- `POST /auth/signup` - Register new user
- `POST /auth/verify-email` - Verify email with OTP
- `POST /auth/signin` - User login
- `POST /auth/forgot-password` - Request password reset
- `POST /auth/reset-password` - Reset password with code
- `POST /auth/logout` - Logout user
- `GET /auth/me` - Get current user profile
- `PUT /auth/profile` - Update user profile
- `DELETE /auth/account` - Delete user account

#### Chat
- `POST /chat/sessions` - Create new chat session
- `GET /chat/sessions` - Get user's chat sessions
- `GET /chat/sessions/{session_id}/messages` - Get session messages
- `DELETE /chat/sessions/{session_id}` - Delete chat session
- `WS /ws/chat/{session_id}` - WebSocket chat endpoint

## Database Schema

The application uses SQLAlchemy with the following main models:

- **User** - User accounts and profiles
- **PendingVerification** - Temporary email verification records
- **PasswordResetToken** - Password reset tokens
- **ChatSession** - Chat conversation sessions
- **ChatMessage** - Individual chat messages
- **UserSession** - User login sessions

## Development

### Running Tests
```bash
# Backend tests
pytest

# Frontend tests (if configured)
cd frontend
npm test
```

### Code Structure

```
├── app.py                 # Main FastAPI application
├── auth.py               # Authentication utilities
├── chat.py               # Chat functionality
├── config.py             # Configuration management
├── database.py           # Database setup
├── models.py             # SQLAlchemy models
├── email_utils.py        # Email utilities
├── requirements.txt      # Python dependencies
├── frontend/             # Next.js frontend
│   ├── src/
│   │   ├── app/         # App router pages
│   │   ├── auth/        # Authentication context
│   │   └── components/  # Reusable components
│   └── package.json     # Node.js dependencies
└── README.md            # This file
```

## Deployment

### Backend Deployment
1. Use a production WSGI server like Gunicorn
2. Configure environment variables for production
3. Use a production database (PostgreSQL recommended)
4. Set up reverse proxy (Nginx)
5. Configure SSL/TLS certificates

### Frontend Deployment
1. Build the production version: `npm run build`
2. Deploy to platforms like Vercel, Netlify, or traditional hosting
3. Configure environment variables for production API URL

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the API documentation at `/docs`
- Review the code comments and docstrings
- Open an issue on the repository

## Roadmap

Future enhancements planned:
- [ ] File upload support in chat
- [ ] Group chat functionality
- [ ] Push notifications
- [ ] Advanced AI integration
- [ ] Mobile app development
- [ ] Advanced user roles and permissions
