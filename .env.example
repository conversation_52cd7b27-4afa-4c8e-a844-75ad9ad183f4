# Database Configuration
DATABASE_URL="sqlite:///./app_database.db"

# JWT Configuration
JWT_SECRET_KEY="your-super-secret-jwt-key-change-this-in-production"
JWT_ALGORITHM="HS256"
JWT_EXPIRATION_HOURS=24

# Email Configuration (SMTP)
SMTP_SERVER="smtp.gmail.com"
SMTP_PORT=587
EMAIL_ADDRESS="<EMAIL>"
EMAIL_PASSWORD="your-app-password"

# OTP Configuration
OTP_EXPIRATION_MINUTES=15
RESET_TOKEN_EXPIRATION_MINUTES=30

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=3600

# CORS Configuration
ALLOWED_ORIGINS="http://localhost:3000,http://127.0.0.1:3000,http://localhost:3001,http://127.0.0.1:3001"

# Application Configuration
APP_NAME="Athlix"
APP_VERSION="1.0.0"
DEBUG=true

# Session Configuration
SESSION_SECRET_KEY="your-session-secret-key-change-this"
SESSION_EXPIRATION_HOURS=168
