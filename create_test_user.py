"""
<PERSON><PERSON><PERSON> to create a verified test user for testing signin functionality.
"""
from datetime import datetime, date
from sqlalchemy.orm import Session
from database import get_db, engine
from models import User
from auth import hash_password
import uuid

def create_test_user():
    """Create a verified test user."""
    # Create database session
    db = Session(bind=engine)
    
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == "<EMAIL>").first()
        if existing_user:
            print("User already exists. Updating to verified status...")
            existing_user.is_verified = True
            existing_user.is_active = True
            db.commit()
            print("✅ User updated successfully!")
            return existing_user
        
        # Create new user
        user_data = {
            "id": str(uuid.uuid4()),
            "email": "<EMAIL>",
            "username": "testuser",
            "first_name": "Test",
            "last_name": "User",
            "password_hash": hash_password("1234Qwer"),
            "date_of_birth": date(1990, 1, 1),
            "is_verified": True,
            "is_active": True,
            "created_at": datetime.utcnow()
        }
        
        user = User(**user_data)
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print("✅ Test user created successfully!")
        print(f"   Email: {user.email}")
        print(f"   Password: 1234Qwer")
        print(f"   Verified: {user.is_verified}")
        print(f"   Active: {user.is_active}")
        
        return user
        
    except Exception as e:
        print(f"❌ Error creating user: {e}")
        db.rollback()
        return None
    finally:
        db.close()

if __name__ == "__main__":
    print("Creating test user...")
    create_test_user()
