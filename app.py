"""
Full-Stack Web Application with Authentication and Real-time Chat
"""
import json
import uuid
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from fastapi import FastAPI, HTTPException, status, Depends, BackgroundTasks, WebSocket, WebSocketDisconnect, Request, Response, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel, EmailStr, validator
from sqlalchemy.orm import Session
import logging

# Local imports
from config import settings
from database import get_db, init_database
from models import User, PendingVerification, PasswordResetToken, ChatSession, ChatMessage, UserSession
from auth import (
    hash_password, verify_password, generate_verification_code, generate_reset_token,
    create_access_token, create_refresh_token, verify_jwt_token, get_current_user, get_optional_current_user,
    create_user_session, invalidate_user_sessions, cleanup_expired_sessions, security, validate_refresh_token,
    set_auth_cookies, clear_auth_cookies, get_current_user_from_request
)
from email_utils import (
    send_email_background, create_verification_email_html, create_password_reset_email_html
)
from chat import (
    manager, create_chat_session, save_chat_message, get_chat_history,
    get_user_chat_sessions, delete_chat_session, handle_websocket_message
)
from file_utils import save_uploaded_image, resize_image_if_needed

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="A comprehensive full-stack web application with authentication and real-time chat"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize database on startup
@app.on_event("startup")
async def startup_event():
    """Initialize database and perform startup tasks."""
    try:
        # Check if migration is needed first
        try:
            from migrate_database import verify_migration
            if not verify_migration():
                logger.warning("Database schema needs migration. Running migration...")
                from migrate_database import migrate_user_sessions_table
                migrate_user_sessions_table()
                logger.info("Database migration completed")
        except Exception as migration_error:
            logger.warning(f"Migration check failed: {migration_error}")

        # Initialize database
        init_database()
        logger.info("Application started successfully")
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise

# Cleanup expired sessions periodically
@app.on_event("startup")
async def setup_periodic_tasks():
    """Setup periodic cleanup tasks."""
    import asyncio

    async def cleanup_task():
        while True:
            try:
                db = next(get_db())
                cleanup_expired_sessions(db)
                db.close()
                await asyncio.sleep(3600)  # Run every hour
            except Exception as e:
                logger.error(f"Cleanup task error: {e}")
                await asyncio.sleep(3600)

    asyncio.create_task(cleanup_task())

# Pydantic Models for API
class UserSignup(BaseModel):
    """User signup request model."""
    email: EmailStr
    username: Optional[str] = None
    first_name: str
    last_name: str
    password: str
    date_of_birth: Optional[str] = None

    @validator('username')
    def validate_username(cls, v):
        if v is not None:
            if len(v) < 3:
                raise ValueError('Username must be at least 3 characters long')
            if len(v) > 30:
                raise ValueError('Username must be less than 30 characters')
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
            return v.lower()
        return v

    @validator('first_name', 'last_name')
    def validate_names(cls, v):
        if len(v) < 2:
            raise ValueError('Name must be at least 2 characters long')
        if len(v) > 50:
            raise ValueError('Name must be less than 50 characters')
        return v.strip().title()

    @validator('password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class EmailVerification(BaseModel):
    """Email verification request model."""
    email: EmailStr
    verification_code: str

class UserSignin(BaseModel):
    """User signin request model."""
    email: EmailStr
    password: str

class ForgotPassword(BaseModel):
    """Forgot password request model."""
    email: EmailStr

class ResetPassword(BaseModel):
    """Reset password request model."""
    email: EmailStr
    reset_code: str
    new_password: str

    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters long')
        if not any(c.isupper() for c in v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not any(c.islower() for c in v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not any(c.isdigit() for c in v):
            raise ValueError('Password must contain at least one digit')
        return v

class RefreshTokenRequest(BaseModel):
    """Refresh token request model."""
    refresh_token: str

class UserProfile(BaseModel):
    """User profile update model."""
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    bio: Optional[str] = None

    @validator('first_name', 'last_name')
    def validate_names(cls, v):
        if v is not None:
            if len(v) < 2:
                raise ValueError('Name must be at least 2 characters long')
            if len(v) > 50:
                raise ValueError('Name must be less than 50 characters')
            return v.strip().title()
        return v

    @validator('bio')
    def validate_bio(cls, v):
        if v is not None and len(v) > 500:
            raise ValueError('Bio must be less than 500 characters')
        return v

class ChatMessageRequest(BaseModel):
    """Chat message request model."""
    content: str
    session_id: Optional[str] = None

class ChatSessionCreate(BaseModel):
    """Chat session creation model."""
    title: Optional[str] = None

# Response Models
class UserResponse(BaseModel):
    """User response model."""
    id: str
    email: str
    username: str
    first_name: str
    last_name: str
    is_verified: bool
    is_active: bool
    created_at: datetime
    profile_picture: Optional[str] = None
    bio: Optional[str] = None

class SigninResponse(BaseModel):
    """Signin response model."""
    access_token: str
    refresh_token: str
    token_type: str
    expires_in: int
    user: UserResponse

class ChatMessageResponse(BaseModel):
    """Chat message response model."""
    id: str
    content: str
    is_user: bool
    message_type: str
    image_url: Optional[str] = None
    file_url: Optional[str] = None
    created_at: datetime

class ChatSessionResponse(BaseModel):
    """Chat session response model."""
    id: str
    title: str
    created_at: datetime
    last_activity: datetime
    message_count: Optional[int] = None

# API Endpoints

# Health check
@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "message": f"{settings.APP_NAME} is running",
        "version": settings.APP_VERSION,
        "status": "healthy"
    }

# Authentication Endpoints
@app.post("/auth/signup", status_code=status.HTTP_201_CREATED)
async def signup(
    user_data: UserSignup,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Register a new user account."""
    # Check if email already exists
    existing_user = db.query(User).filter(User.email == user_data.email.lower()).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Email already registered"
        )

    # Generate username if not provided
    if user_data.username is None:
        # Generate username from email (part before @)
        email_prefix = user_data.email.split('@')[0].lower()
        # Clean up the username to make it valid
        import re
        username = re.sub(r'[^a-z0-9_-]', '', email_prefix)
        if len(username) < 3:
            username = f"user_{username}"

        # Ensure username is unique
        counter = 1
        original_username = username
        while db.query(User).filter(User.username == username).first():
            username = f"{original_username}_{counter}"
            counter += 1

        user_data.username = username
    else:
        # Check if username already exists
        existing_username = db.query(User).filter(User.username == user_data.username.lower()).first()
        if existing_username:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )

    # Check for pending verification
    pending = db.query(PendingVerification).filter(
        PendingVerification.email == user_data.email.lower()
    ).first()

    if pending:
        if datetime.utcnow() > pending.expires_at:
            db.delete(pending)
            db.commit()
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email verification already pending. Please check your email."
            )

    # Generate verification code and hash password
    verification_code = generate_verification_code()
    hashed_password = hash_password(user_data.password)

    # Store user data temporarily
    temp_user_data = {
        "email": user_data.email.lower(),
        "username": user_data.username.lower(),
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "password_hash": hashed_password,
        "date_of_birth": user_data.date_of_birth
    }

    # Create pending verification record
    pending_verification = PendingVerification(
        email=user_data.email.lower(),
        verification_code=verification_code,
        expires_at=datetime.utcnow() + timedelta(minutes=settings.OTP_EXPIRATION_MINUTES),
        user_data=json.dumps(temp_user_data)
    )

    db.add(pending_verification)
    db.commit()

    # Send verification email
    html_body = create_verification_email_html(verification_code, user_data.first_name)
    background_tasks.add_task(
        send_email_background,
        user_data.email.lower(),
        "Verify Your Account",
        html_body
    )

    return {
        "message": "Account created successfully! Please check your email for verification code.",
        "email": user_data.email.lower()
    }

@app.post("/auth/verify-email")
async def verify_email(
    verification_data: EmailVerification,
    db: Session = Depends(get_db)
):
    """Verify email address with verification code."""
    email = verification_data.email.lower()

    pending = db.query(PendingVerification).filter(
        PendingVerification.email == email
    ).first()

    if not pending:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No pending verification found for this email"
        )

    if datetime.utcnow() > pending.expires_at:
        db.delete(pending)
        db.commit()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Verification code has expired. Please sign up again."
        )

    if verification_data.verification_code != pending.verification_code:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid verification code"
        )

    # Create user account
    user_data = json.loads(pending.user_data)
    new_user = User(
        email=user_data["email"],
        username=user_data["username"],
        first_name=user_data["first_name"],
        last_name=user_data["last_name"],
        password_hash=user_data["password_hash"],
        is_verified=True,
        verified_at=datetime.utcnow()
    )

    if user_data.get("date_of_birth"):
        try:
            from datetime import date
            new_user.date_of_birth = date.fromisoformat(user_data["date_of_birth"])
        except:
            pass  # Skip if date parsing fails

    db.add(new_user)
    db.delete(pending)
    db.commit()
    db.refresh(new_user)

    return {
        "message": "Email verified successfully! Your account is now active.",
        "user_id": new_user.id
    }

@app.post("/auth/signin", response_model=SigninResponse)
async def signin(
    signin_data: UserSignin,
    request: Request,
    response: Response,
    db: Session = Depends(get_db)
):
    """Authenticate user and return access token."""
    email = signin_data.email.lower()

    user = db.query(User).filter(User.email == email).first()
    if not user or not verify_password(signin_data.password, user.password_hash):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid email or password"
        )

    if not user.is_verified:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Please verify your email address first"
        )

    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Account is deactivated"
        )

    # Create JWT tokens
    token_data = {
        "id": user.id,
        "email": user.email,
        "username": user.username
    }
    access_token = create_access_token(token_data)
    refresh_token = create_refresh_token(token_data)

    # Create user session
    ip_address = request.client.host if request.client else None
    user_agent = request.headers.get("user-agent")

    create_user_session(
        user=user,
        access_token=access_token,
        refresh_token=refresh_token,
        ip_address=ip_address,
        user_agent=user_agent,
        db=db
    )

    # Update last login
    user.last_login = datetime.utcnow()
    db.commit()

    user_response = UserResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        is_verified=user.is_verified,
        is_active=user.is_active,
        created_at=user.created_at,
        profile_picture=user.profile_picture,
        bio=user.bio
    )

    # Set authentication cookies
    set_auth_cookies(response, access_token, refresh_token)

    return SigninResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="bearer",
        expires_in=settings.JWT_EXPIRATION_HOURS * 3600,
        user=user_response
    )

@app.post("/auth/forgot-password")
async def forgot_password(
    forgot_data: ForgotPassword,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Initiate password reset process."""
    email = forgot_data.email.lower()

    user = db.query(User).filter(User.email == email).first()
    if not user:
        # Return success message even if user doesn't exist for security
        return {"message": "If the email exists, a password reset code has been sent."}

    # Remove any existing reset tokens for this email
    db.query(PasswordResetToken).filter(PasswordResetToken.email == email).delete()

    # Generate reset token
    reset_code = generate_reset_token()

    # Create reset token record
    reset_token = PasswordResetToken(
        email=email,
        reset_code=reset_code,
        expires_at=datetime.utcnow() + timedelta(minutes=settings.RESET_TOKEN_EXPIRATION_MINUTES)
    )

    db.add(reset_token)
    db.commit()

    # Send reset email
    html_body = create_password_reset_email_html(reset_code, user.first_name)
    background_tasks.add_task(
        send_email_background,
        email,
        "Password Reset Request",
        html_body
    )

    return {"message": "If the email exists, a password reset code has been sent."}

@app.post("/auth/reset-password")
async def reset_password(
    reset_data: ResetPassword,
    db: Session = Depends(get_db)
):
    """Reset password using reset code."""
    email = reset_data.email.lower()

    # Find valid reset token
    reset_token = db.query(PasswordResetToken).filter(
        PasswordResetToken.email == email,
        PasswordResetToken.reset_code == reset_data.reset_code,
        PasswordResetToken.used == False
    ).first()

    if not reset_token:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid or expired reset code"
        )

    if datetime.utcnow() > reset_token.expires_at:
        db.delete(reset_token)
        db.commit()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Reset code has expired"
        )

    # Find user and update password
    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User not found"
        )

    # Update password
    user.password_hash = hash_password(reset_data.new_password)

    # Mark reset token as used
    reset_token.used = True

    # Invalidate all user sessions
    invalidate_user_sessions(user.id, db)

    db.commit()

    return {"message": "Password reset successfully. Please sign in with your new password."}

@app.post("/auth/resend-verification")
async def resend_verification(
    email_data: ForgotPassword,  # Reuse the same model since it only needs email
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db)
):
    """Resend verification email."""
    email = email_data.email.lower()

    # Check if there's a pending verification
    pending = db.query(PendingVerification).filter(
        PendingVerification.email == email
    ).first()

    if not pending:
        # Check if user already exists and is verified
        user = db.query(User).filter(User.email == email).first()
        if user and user.is_verified:
            return {"message": "Email is already verified."}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No pending verification found for this email. Please sign up again."
            )

    # Generate new verification code
    verification_code = generate_verification_code()

    # Update the pending verification with new code and expiration
    pending.verification_code = verification_code
    pending.expires_at = datetime.utcnow() + timedelta(minutes=settings.OTP_EXPIRATION_MINUTES)

    db.commit()

    # Get user data from pending verification
    user_data = json.loads(pending.user_data)

    # Send verification email
    html_body = create_verification_email_html(verification_code, user_data["first_name"])
    background_tasks.add_task(
        send_email_background,
        email,
        "Verify Your Account",
        html_body
    )

    return {"message": "Verification code resent successfully! Please check your email."}

@app.post("/auth/refresh", response_model=SigninResponse)
async def refresh_access_token(
    refresh_request: Optional[RefreshTokenRequest] = None,
    request: Request = None,
    response: Response = None,
    db: Session = Depends(get_db)
):
    """Refresh access token using refresh token."""
    # Try to get refresh token from cookies first, then from request body
    refresh_token = None
    if request:
        refresh_token = request.cookies.get(settings.REFRESH_TOKEN_COOKIE_NAME)

    if not refresh_token and refresh_request:
        refresh_token = refresh_request.refresh_token

    if not refresh_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token not provided"
        )

    session = validate_refresh_token(refresh_token, db)

    if not session:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid or expired refresh token"
        )

    # Get user
    user = db.query(User).filter(User.id == session.user_id).first()
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )

    # Create new tokens
    token_data = {
        "id": user.id,
        "email": user.email,
        "username": user.username
    }
    new_access_token = create_access_token(token_data)
    new_refresh_token = create_refresh_token(token_data)

    # Update session with new tokens
    session.access_token = new_access_token
    session.refresh_token = new_refresh_token
    session.access_expires_at = datetime.utcnow() + timedelta(hours=settings.JWT_EXPIRATION_HOURS)
    session.refresh_expires_at = datetime.utcnow() + timedelta(days=30)
    session.last_activity = datetime.utcnow()

    # Update IP and user agent if provided
    if request.client:
        session.ip_address = request.client.host
    session.user_agent = request.headers.get("user-agent")

    db.commit()

    user_response = UserResponse(
        id=user.id,
        email=user.email,
        username=user.username,
        first_name=user.first_name,
        last_name=user.last_name,
        is_verified=user.is_verified,
        is_active=user.is_active,
        created_at=user.created_at,
        profile_picture=user.profile_picture,
        bio=user.bio
    )

    # Set authentication cookies if response is available
    if response:
        set_auth_cookies(response, new_access_token, new_refresh_token)

    return SigninResponse(
        access_token=new_access_token,
        refresh_token=new_refresh_token,
        token_type="bearer",
        expires_in=settings.JWT_EXPIRATION_HOURS * 3600,
        user=user_response
    )

@app.post("/auth/logout")
async def logout(
    response: Response,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Logout user and invalidate sessions."""
    invalidate_user_sessions(current_user.id, db)
    clear_auth_cookies(response)
    return {"message": "Logged out successfully"}

# User Profile Endpoints
@app.get("/auth/me", response_model=UserResponse)
async def get_current_user_profile(current_user: User = Depends(get_current_user)):
    """Get current user profile."""
    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        username=current_user.username,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        is_verified=current_user.is_verified,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        profile_picture=current_user.profile_picture,
        bio=current_user.bio
    )

@app.put("/auth/profile", response_model=UserResponse)
async def update_profile(
    profile_data: UserProfile,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update user profile."""
    if profile_data.first_name is not None:
        current_user.first_name = profile_data.first_name

    if profile_data.last_name is not None:
        current_user.last_name = profile_data.last_name

    if profile_data.bio is not None:
        current_user.bio = profile_data.bio

    db.commit()
    db.refresh(current_user)

    return UserResponse(
        id=current_user.id,
        email=current_user.email,
        username=current_user.username,
        first_name=current_user.first_name,
        last_name=current_user.last_name,
        is_verified=current_user.is_verified,
        is_active=current_user.is_active,
        created_at=current_user.created_at,
        profile_picture=current_user.profile_picture,
        bio=current_user.bio
    )

@app.delete("/auth/account")
async def delete_account(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete user account (soft delete)."""
    current_user.is_active = False
    invalidate_user_sessions(current_user.id, db)
    db.commit()

    return {"message": "Account deactivated successfully"}

# Chat Endpoints
@app.post("/chat/sessions", response_model=ChatSessionResponse)
async def create_new_chat_session(
    session_data: ChatSessionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new chat session."""
    session = create_chat_session(
        user_id=current_user.id,
        title=session_data.title,
        db=db
    )

    return ChatSessionResponse(
        id=session.id,
        title=session.title,
        created_at=session.created_at,
        last_activity=session.last_activity,
        message_count=0
    )

@app.get("/chat/sessions", response_model=List[ChatSessionResponse])
async def get_chat_sessions(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all chat sessions for the current user."""
    sessions = get_user_chat_sessions(current_user.id, db)

    session_responses = []
    for session in sessions:
        # Count messages in session
        message_count = db.query(ChatMessage).filter(
            ChatMessage.session_id == session.id
        ).count()

        session_responses.append(ChatSessionResponse(
            id=session.id,
            title=session.title,
            created_at=session.created_at,
            last_activity=session.last_activity,
            message_count=message_count
        ))

    return session_responses

@app.get("/chat/sessions/{session_id}/messages", response_model=List[ChatMessageResponse])
async def get_chat_messages(
    session_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get messages for a specific chat session."""
    # Verify session belongs to user
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )

    messages = get_chat_history(session_id, limit=100, db=db)

    return [
        ChatMessageResponse(
            id=msg.id,
            content=msg.content,
            is_user=msg.is_user,
            message_type=msg.message_type,
            image_url=msg.image_url,
            file_url=msg.file_url,
            created_at=msg.created_at
        )
        for msg in messages
    ]

@app.delete("/chat/sessions/{session_id}")
async def delete_chat_session_endpoint(
    session_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a chat session."""
    success = delete_chat_session(session_id, current_user.id, db)

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )

    return {"message": "Chat session deleted successfully"}

# Image Upload Endpoint
@app.post("/chat/upload-image")
async def upload_image(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user)
):
    """Upload an image for chat messages."""
    try:
        # Save the uploaded image
        file_path, file_url = await save_uploaded_image(file)

        # Resize image if needed
        resize_image_if_needed(file_path)

        return {
            "success": True,
            "file_url": file_url,
            "filename": file.filename,
            "message": "Image uploaded successfully"
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Image upload error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload image"
        )

# File Serving Endpoint
@app.get("/uploads/{filename}")
async def serve_uploaded_file(filename: str):
    """Serve uploaded files."""
    import os
    file_path = os.path.join(settings.UPLOAD_DIR, filename)

    if not os.path.exists(file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )

    return FileResponse(file_path)

# Enhanced Chat Session Management Endpoints
@app.put("/chat/sessions/{session_id}")
async def update_chat_session(
    session_id: str,
    update_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update chat session (e.g., rename)."""
    # Verify session belongs to user
    session = db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == current_user.id
    ).first()

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )

    # Update session title if provided
    if "title" in update_data:
        session.title = update_data["title"]
        db.commit()
        db.refresh(session)

    return {
        "id": session.id,
        "title": session.title,
        "message": "Session updated successfully"
    }

@app.get("/chat/sessions/search")
async def search_chat_sessions(
    q: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Search chat sessions by title or content."""
    if not q.strip():
        return []

    # Search sessions by title
    sessions = db.query(ChatSession).filter(
        ChatSession.user_id == current_user.id,
        ChatSession.title.ilike(f"%{q}%")
    ).order_by(ChatSession.last_activity.desc()).limit(20).all()

    session_responses = []
    for session in sessions:
        message_count = db.query(ChatMessage).filter(
            ChatMessage.session_id == session.id
        ).count()

        session_responses.append({
            "id": session.id,
            "title": session.title,
            "created_at": session.created_at,
            "last_activity": session.last_activity,
            "message_count": message_count
        })

    return session_responses

# WebSocket endpoint for real-time chat
@app.websocket("/ws/chat/{session_id}")
async def websocket_chat_endpoint(
    websocket: WebSocket,
    session_id: str,
    token: str,
    db: Session = Depends(get_db)
):
    """WebSocket endpoint for real-time chat."""
    try:
        # Verify JWT token
        payload = verify_jwt_token(token)
        user_id = payload.get("user_id")

        if not user_id:
            await websocket.close(code=4001, reason="Invalid token")
            return

        # Verify user exists and is active
        user = db.query(User).filter(User.id == user_id).first()
        if not user or not user.is_active:
            await websocket.close(code=4002, reason="User not found or inactive")
            return

        # Verify or create chat session
        session = db.query(ChatSession).filter(
            ChatSession.id == session_id,
            ChatSession.user_id == user_id
        ).first()

        if not session:
            # Create new session if it doesn't exist
            session = create_chat_session(user_id=user_id, db=db)
            session_id = session.id

        # Connect to WebSocket manager
        await manager.connect(websocket, user_id, session_id)

        # Send connection confirmation
        await websocket.send_text(json.dumps({
            "type": "connected",
            "session_id": session_id,
            "message": "Connected to chat session"
        }))

        # Handle incoming messages
        while True:
            try:
                data = await websocket.receive_text()
                message_data = json.loads(data)
                await handle_websocket_message(websocket, message_data, db)
            except WebSocketDisconnect:
                break
            except json.JSONDecodeError:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "Invalid JSON format"
                }))
            except Exception as e:
                logger.error(f"WebSocket message handling error: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": "An error occurred while processing your message"
                }))

    except HTTPException as e:
        await websocket.close(code=4003, reason=str(e.detail))
    except Exception as e:
        logger.error(f"WebSocket connection error: {e}")
        await websocket.close(code=4000, reason="Internal server error")
    finally:
        manager.disconnect(websocket)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG
    )