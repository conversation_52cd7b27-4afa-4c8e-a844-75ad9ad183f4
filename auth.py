"""
Authentication utilities and security functions.
"""
import hashlib
import secrets
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from fastapi import HTTPException, status, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from passlib.context import Crypt<PERSON>ontext
from jose import jwt

from config import settings
from database import get_db
from models import User, UserSession

# Setup logger
logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Security scheme for JWT authentication
security = HTTPBearer()

def hash_password(password: str) -> str:
    """Hash a password using bcrypt."""
    return pwd_context.hash(password)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)

def generate_verification_code() -> str:
    """Generate a 6-digit verification code."""
    return ''.join([str(secrets.randbelow(10)) for _ in range(6)])

def generate_reset_token() -> str:
    """Generate a secure reset token."""
    return secrets.token_urlsafe(32)

def generate_session_token() -> str:
    """Generate a secure session token."""
    return secrets.token_urlsafe(64)

def create_access_token(user_data: Dict[str, Any]) -> str:
    """Create an access JWT token for the given user data."""
    payload = {
        "user_id": user_data["id"],
        "email": user_data["email"],
        "username": user_data.get("username", ""),
        "exp": datetime.utcnow() + timedelta(hours=settings.JWT_EXPIRATION_HOURS),
        "iat": datetime.utcnow(),
        "type": "access"
    }
    return jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

def create_refresh_token(user_data: Dict[str, Any]) -> str:
    """Create a refresh JWT token for the given user data."""
    payload = {
        "user_id": user_data["id"],
        "email": user_data["email"],
        "exp": datetime.utcnow() + timedelta(days=30),  # Refresh tokens last 30 days
        "iat": datetime.utcnow(),
        "type": "refresh"
    }
    return jwt.encode(payload, settings.JWT_SECRET_KEY, algorithm=settings.JWT_ALGORITHM)

# Keep backward compatibility
def create_jwt_token(user_data: Dict[str, Any]) -> str:
    """Create a JWT token for the given user data (backward compatibility)."""
    return create_access_token(user_data)

def verify_jwt_token(token: str) -> Dict[str, Any]:
    """Verify and decode a JWT token."""
    try:
        payload = jwt.decode(token, settings.JWT_SECRET_KEY, algorithms=[settings.JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired"
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid token"
        )

async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """
    Get the current authenticated user from JWT token.
    This dependency can be used to protect routes.
    """
    token = credentials.credentials
    payload = verify_jwt_token(token)
    
    user_id = payload.get("user_id")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials"
        )
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is deactivated"
        )
    
    return user

async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> Optional[User]:
    """
    Get the current user if authenticated, otherwise return None.
    This dependency can be used for optional authentication.
    """
    if not credentials:
        return None
    
    try:
        return await get_current_user(credentials, db)
    except HTTPException:
        return None

def create_user_session(
    user: User,
    access_token: str,
    refresh_token: str,
    ip_address: Optional[str] = None,
    user_agent: Optional[str] = None,
    db: Session = None
) -> UserSession:
    """Create a new user session record."""
    session_token = generate_session_token()
    access_expires_at = datetime.utcnow() + timedelta(hours=settings.JWT_EXPIRATION_HOURS)
    refresh_expires_at = datetime.utcnow() + timedelta(days=30)

    user_session = UserSession(
        user_id=user.id,
        session_token=session_token,
        access_token=access_token,
        refresh_token=refresh_token,
        ip_address=ip_address,
        user_agent=user_agent,
        access_expires_at=access_expires_at,
        refresh_expires_at=refresh_expires_at
    )

    if db:
        db.add(user_session)
        db.commit()
        db.refresh(user_session)

    return user_session

def invalidate_user_sessions(user_id: str, db: Session, exclude_session_id: Optional[str] = None):
    """Invalidate all user sessions except the specified one."""
    query = db.query(UserSession).filter(UserSession.user_id == user_id)
    
    if exclude_session_id:
        query = query.filter(UserSession.id != exclude_session_id)
    
    query.update({"is_active": False})
    db.commit()

def cleanup_expired_sessions(db: Session):
    """Clean up expired sessions from the database."""
    try:
        now = datetime.utcnow()

        # Try to use new schema first
        try:
            expired_sessions = db.query(UserSession).filter(
                (UserSession.access_expires_at < now) | (UserSession.refresh_expires_at < now)
            )
            expired_sessions.update({"is_active": False})
            db.commit()
            logger.info("Cleaned up expired sessions using new schema")
        except Exception as e:
            # If new schema fails, try old schema (for backward compatibility during migration)
            logger.warning(f"New schema cleanup failed, trying old schema: {e}")
            try:
                # This will work with old schema that has 'expires_at' column
                expired_sessions = db.query(UserSession).filter(
                    UserSession.expires_at < now
                )
                expired_sessions.update({"is_active": False})
                db.commit()
                logger.info("Cleaned up expired sessions using old schema")
            except Exception as e2:
                logger.error(f"Both cleanup methods failed: {e2}")
                # Don't raise the error to prevent app startup failure
                pass

    except Exception as e:
        logger.error(f"Session cleanup failed: {e}")
        # Don't raise the error to prevent app startup failure

def validate_refresh_token(refresh_token: str, db: Session) -> Optional[UserSession]:
    """Validate a refresh token and return the associated session."""
    try:
        payload = verify_jwt_token(refresh_token)
        if payload.get("type") != "refresh":
            return None

        user_id = payload.get("user_id")
        if not user_id:
            return None

        # Find active session with this refresh token
        session = db.query(UserSession).filter(
            UserSession.refresh_token == refresh_token,
            UserSession.user_id == user_id,
            UserSession.is_active == True,
            UserSession.refresh_expires_at > datetime.utcnow()
        ).first()

        return session
    except HTTPException:
        return None
