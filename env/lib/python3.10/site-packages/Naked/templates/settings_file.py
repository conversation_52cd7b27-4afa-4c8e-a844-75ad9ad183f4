#!/usr/bin/env python
# encoding: utf-8

# VARS = app_name
settings_file_string = """
#!/usr/bin/env python
# encoding: utf-8

#------------------------------------------------------------------------------
# Application Name
#------------------------------------------------------------------------------
app_name = '{{app_name}}'

#------------------------------------------------------------------------------
# Version Number
#------------------------------------------------------------------------------
major_version = "0"
minor_version = "1"
patch_version = "0"

#------------------------------------------------------------------------------
# Debug Flag (switch to False for production release code)
#------------------------------------------------------------------------------
debug = True

#------------------------------------------------------------------------------
# Usage String
#------------------------------------------------------------------------------
usage = ''

#------------------------------------------------------------------------------
# Help String
#------------------------------------------------------------------------------
help = ''
"""
