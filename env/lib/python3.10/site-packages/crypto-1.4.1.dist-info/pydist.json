{"license": "MIT license", "exports": {"console_scripts": {"crypto": "crypto.app:main", "decrypto": "crypto.decryptoapp:main"}}, "document_names": {"description": "DESCRIPTION.rst"}, "name": "crypto", "metadata_version": "2.0", "contacts": [{"role": "author", "email": "<EMAIL>", "name": "<PERSON>"}], "generator": "bdist_wheel (0.22.0)", "commands": {"wrap_console": {"crypto": "crypto.app:main", "decrypto": "crypto.decryptoapp:main"}}, "summary": "Simple symmetric GPG file encryption and decryption", "project_urls": {"Home": "https://github.com/chrissimpkins/crypto"}, "platform": "any", "run_requires": [{"requires": ["Naked", "shellescape"]}], "version": "1.4.1", "keywords": "encryption,decryption,gpg,pgp,openpgp,cipher,AES256,crypto,cryptography,security,privacy", "classifiers": ["Intended Audience :: End Users/Desktop", "Topic :: Security :: Cryptography", "Topic :: Security", "Development Status :: 5 - Production/Stable", "Natural Language :: English", "License :: OSI Approved :: MIT License", "Operating System :: MacOS :: MacOS X", "Operating System :: POSIX", "Operating System :: Unix", "Programming Language :: Python", "Programming Language :: Python :: 2", "Programming Language :: Python :: 3"], "extras": []}