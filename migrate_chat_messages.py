"""
Database migration script to add image and file URL columns to chat_messages table.
"""
import sqlite3
import os
from config import settings

def check_column_exists(cursor, table_name, column_name):
    """Check if a column exists in a table."""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = [column[1] for column in cursor.fetchall()]
    return column_name in columns

def migrate_chat_messages_table():
    """Add image_url and file_url columns to chat_messages table."""
    db_path = settings.DATABASE_URL.replace("sqlite:///", "").replace("./", "")
    
    print(f"Migrating database: {db_path}")
    
    # Connect to database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check if the table exists
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='chat_messages'
        """)
        
        if not cursor.fetchone():
            print("chat_messages table doesn't exist. It will be created by SQLAlchemy.")
            return
        
        # Check if we need to add the new columns
        has_image_url = check_column_exists(cursor, 'chat_messages', 'image_url')
        has_file_url = check_column_exists(cursor, 'chat_messages', 'file_url')
        
        print(f"Schema check:")
        print(f"  - has_image_url: {has_image_url}")
        print(f"  - has_file_url: {has_file_url}")
        
        # Add image_url column if it doesn't exist
        if not has_image_url:
            print("Adding image_url column...")
            cursor.execute("ALTER TABLE chat_messages ADD COLUMN image_url VARCHAR")
            print("Added image_url column.")
        
        # Add file_url column if it doesn't exist
        if not has_file_url:
            print("Adding file_url column...")
            cursor.execute("ALTER TABLE chat_messages ADD COLUMN file_url VARCHAR")
            print("Added file_url column.")
        
        if has_image_url and has_file_url:
            print("Database schema is already up to date.")
        else:
            conn.commit()
            print("Migration completed successfully!")
        
    except Exception as e:
        print(f"Migration failed: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()

def verify_migration():
    """Verify that the migration was successful."""
    db_path = settings.DATABASE_URL.replace("sqlite:///", "").replace("./", "")
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # Check table structure
        cursor.execute("PRAGMA table_info(chat_messages)")
        columns = cursor.fetchall()
        
        print("\nVerification - chat_messages table structure:")
        for column in columns:
            print(f"  {column[1]} {column[2]} {'NOT NULL' if column[3] else 'NULL'}")
        
        # Check for required columns
        column_names = [column[1] for column in columns]
        required_columns = ['image_url', 'file_url']
        
        missing_columns = [col for col in required_columns if col not in column_names]
        
        if missing_columns:
            print(f"❌ Missing columns: {missing_columns}")
            return False
        else:
            print("✅ All required columns present.")
            return True
            
    except Exception as e:
        print(f"Verification failed: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    print("Starting database migration for chat messages image support...")
    migrate_chat_messages_table()
    
    print("\nVerifying migration...")
    if verify_migration():
        print("\n✅ Database migration completed successfully!")
        print("Chat messages now support image and file URLs.")
    else:
        print("\n❌ Migration verification failed!")
